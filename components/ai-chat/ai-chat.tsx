"use client";

import { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { 
  Send, 
  Bot, 
  User, 
  Loader2, 
  TrendingUp, 
  DollarSign, 
  Target, 
  PieChart 
} from 'lucide-react';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  suggestions?: string[];
}

export function AiChat() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: "Hello! I'm your AI financial assistant. I can help you analyze your spending, create budgets, track goals, and answer questions about your finances. What would you like to know?",
      sender: 'ai',
      timestamp: new Date(),
      suggestions: [
        "What's my spending pattern this month?",
        "How can I save more money?",
        "Set up a new budget",
        "Analyze my investment portfolio"
      ]
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  console.log("AiChat component rendered with", messages.length, "messages");

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage.trim(),
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    console.log("User message sent:", userMessage.content);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse = generateAIResponse(userMessage.content);
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: aiResponse.content,
        sender: 'ai',
        timestamp: new Date(),
        suggestions: aiResponse.suggestions,
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsLoading(false);
      console.log("AI response generated:", aiResponse.content);
    }, 1500);
  };

  const generateAIResponse = (userInput: string): { content: string; suggestions?: string[] } => {
    const input = userInput.toLowerCase();

    if (input.includes('spending') || input.includes('expense')) {
      return {
        content: "Based on your recent transaction data, you've spent $3,248 this month. Your top spending categories are: Food & Dining (28%), Transportation (18%), and Shopping (15%). Compared to last month, you're spending 8.2% less, which is great progress!",
        suggestions: [
          "Show me food spending breakdown",
          "How to reduce transportation costs?",
          "Set spending alerts",
          "Compare with last month"
        ]
      };
    }

    if (input.includes('budget')) {
      return {
        content: "I can help you create a budget! Currently, you have budgets set for 4 categories. Your Food & Dining budget is 70% used ($420/$600), and Shopping is at 85% ($340/$400). Would you like me to suggest adjustments or create new budget categories?",
        suggestions: [
          "Adjust existing budgets",
          "Create new budget category",
          "Set budget alerts",
          "View budget history"
        ]
      };
    }

    if (input.includes('save') || input.includes('saving')) {
      return {
        content: "Great question! Based on your income and expenses, here are some personalized saving tips: 1) You could save $200/month by reducing dining out frequency, 2) Consider switching to a high-yield savings account, 3) Set up automatic transfers of $300/month to savings. Your current savings rate is 18% - aiming for 20% would be ideal!",
        suggestions: [
          "Set up automatic savings",
          "Find high-yield accounts",
          "Track savings goals",
          "Reduce dining expenses"
        ]
      };
    }

    return {
      content: "I understand you're looking for financial guidance. I can help with budgeting, expense tracking, investment analysis, savings goals, and spending insights. Could you be more specific about what you'd like to explore?",
      suggestions: [
        "Analyze my spending patterns",
        "Help with budgeting",
        "Investment advice",
        "Savings strategies"
      ]
    };
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputMessage(suggestion);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="h-[600px] flex flex-col">
      <Card className="flex-1 flex flex-col">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bot className="h-5 w-5 text-primary" />
            <span>AI Financial Assistant</span>
          </CardTitle>
          <CardDescription>
            Get personalized insights and advice about your finances
          </CardDescription>
        </CardHeader>

        <CardContent className="flex-1 flex flex-col">
          <ScrollArea className="flex-1 pr-4">
            <div className="space-y-4">
              {messages.map((message) => (
                <div key={message.id} className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`flex space-x-3 max-w-[80%] ${message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                    <Avatar className="w-8 h-8">
                      <AvatarFallback>
                        {message.sender === 'user' ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className={`rounded-lg p-3 ${
                      message.sender === 'user' 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-muted'
                    }`}>
                      <p className="text-sm">{message.content}</p>
                      
                      {message.suggestions && (
                        <div className="mt-3 space-y-2">
                          <p className="text-xs opacity-70">Suggested questions:</p>
                          <div className="flex flex-wrap gap-2">
                            {message.suggestions.map((suggestion, index) => (
                              <Badge
                                key={index}
                                variant="outline"
                                className="cursor-pointer hover:bg-primary hover:text-primary-foreground text-xs"
                                onClick={() => handleSuggestionClick(suggestion)}
                              >
                                {suggestion}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      <p className="text-xs opacity-50 mt-2">
                        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
              
              {isLoading && (
                <div className="flex justify-start">
                  <div className="flex space-x-3">
                    <Avatar className="w-8 h-8">
                      <AvatarFallback>
                        <Bot className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                    <div className="bg-muted rounded-lg p-3">
                      <div className="flex items-center space-x-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-sm">AI is thinking...</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          <div className="border-t pt-4 mt-4">
            <div className="flex space-x-2">
              <Input
                placeholder="Ask me anything about your finances..."
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                disabled={isLoading}
                className="flex-1"
              />
              <Button onClick={handleSendMessage} disabled={isLoading || !inputMessage.trim()}>
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-3">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleSuggestionClick("What's my spending pattern this month?")}
          className="flex items-center space-x-2"
        >
          <TrendingUp className="h-4 w-4" />
          <span>Spending</span>
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleSuggestionClick("Help me create a budget")}
          className="flex items-center space-x-2"
        >
          <Target className="h-4 w-4" />
          <span>Budget</span>
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleSuggestionClick("How can I save more money?")}
          className="flex items-center space-x-2"
        >
          <DollarSign className="h-4 w-4" />
          <span>Savings</span>
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleSuggestionClick("Analyze my investment portfolio")}
          className="flex items-center space-x-2"
        >
          <PieChart className="h-4 w-4" />
          <span>Investments</span>
        </Button>
      </div>
    </div>
  );
}