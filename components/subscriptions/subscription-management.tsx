"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { 
  Calendar, 
  DollarSign, 
  AlertTriangle, 
  CheckCircle2, 
  Plus, 
  Settings,
  CreditCard,
  Play,
  Pause
} from 'lucide-react';

interface Subscription {
  id: string;
  name: string;
  category: string;
  amount: number;
  frequency: 'monthly' | 'yearly' | 'weekly';
  nextPayment: string;
  status: 'active' | 'paused' | 'cancelled';
  provider: string;
  description: string;
  autoRenew: boolean;
}

export function SubscriptionManagement() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([
    {
      id: '1',
      name: 'Netflix',
      category: 'Entertainment',
      amount: 15.99,
      frequency: 'monthly',
      nextPayment: '2024-06-15',
      status: 'active',
      provider: 'Netflix Inc.',
      description: 'Premium streaming service',
      autoRenew: true,
    },
    {
      id: '2',
      name: 'Spotify Premium',
      category: 'Entertainment',
      amount: 9.99,
      frequency: 'monthly',
      nextPayment: '2024-06-10',
      status: 'active',
      provider: 'Spotify AB',
      description: 'Music streaming service',
      autoRenew: true,
    },
    {
      id: '3',
      name: 'Adobe Creative Cloud',
      category: 'Software',
      amount: 52.99,
      frequency: 'monthly',
      nextPayment: '2024-06-20',
      status: 'active',
      provider: 'Adobe Inc.',
      description: 'Creative software suite',
      autoRenew: true,
    },
    {
      id: '4',
      name: 'Gym Membership',
      category: 'Health & Fitness',
      amount: 49.99,
      frequency: 'monthly',
      nextPayment: '2024-06-08',
      status: 'active',
      provider: 'FitLife Gym',
      description: 'Premium gym access',
      autoRenew: true,
    },
    {
      id: '5',
      name: 'Cloud Storage',
      category: 'Software',
      amount: 99.99,
      frequency: 'yearly',
      nextPayment: '2024-08-15',
      status: 'active',
      provider: 'CloudStore Pro',
      description: '2TB cloud storage',
      autoRenew: false,
    },
    {
      id: '6',
      name: 'VPN Service',
      category: 'Software',
      amount: 4.99,
      frequency: 'monthly',
      nextPayment: '2024-06-12',
      status: 'paused',
      provider: 'SecureVPN',
      description: 'Privacy protection service',
      autoRenew: true,
    },
  ]);

  console.log("SubscriptionManagement component rendered with", subscriptions.length, "subscriptions");

  const toggleSubscriptionStatus = (id: string) => {
    setSubscriptions(prev => prev.map(sub => 
      sub.id === id 
        ? { ...sub, status: sub.status === 'active' ? 'paused' : 'active' }
        : sub
    ));
    console.log("Subscription status toggled for:", id);
  };

  const toggleAutoRenew = (id: string) => {
    setSubscriptions(prev => prev.map(sub => 
      sub.id === id 
        ? { ...sub, autoRenew: !sub.autoRenew }
        : sub
    ));
    console.log("Auto-renew toggled for:", id);
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'default',
      paused: 'secondary',
      cancelled: 'destructive',
    } as const;
    
    return <Badge variant={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  const getFrequencyMultiplier = (frequency: string) => {
    switch (frequency) {
      case 'weekly': return 52;
      case 'monthly': return 12;
      case 'yearly': return 1;
      default: return 12;
    }
  };

  const totalMonthlyAmount = subscriptions
    .filter(sub => sub.status === 'active')
    .reduce((sum, sub) => {
      const multiplier = getFrequencyMultiplier(sub.frequency);
      return sum + (sub.amount * multiplier / 12);
    }, 0);

  const totalYearlyAmount = totalMonthlyAmount * 12;

  const upcomingPayments = subscriptions
    .filter(sub => sub.status === 'active')
    .sort((a, b) => new Date(a.nextPayment).getTime() - new Date(b.nextPayment).getTime())
    .slice(0, 5);

  const categorySummary = subscriptions
    .filter(sub => sub.status === 'active')
    .reduce((acc, sub) => {
      const monthlyAmount = sub.amount * getFrequencyMultiplier(sub.frequency) / 12;
      acc[sub.category] = (acc[sub.category] || 0) + monthlyAmount;
      return acc;
    }, {} as Record<string, number>);

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{subscriptions.filter(s => s.status === 'active').length}</div>
            <p className="text-xs text-muted-foreground">
              Currently active services
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Total</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalMonthlyAmount.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              Average monthly spending
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Yearly Total</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalYearlyAmount.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              Annual subscription cost
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Next Payment</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {upcomingPayments.length > 0 ? 
                new Date(upcomingPayments[0].nextPayment).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }) : 
                'None'
              }
            </div>
            <p className="text-xs text-muted-foreground">
              Upcoming payment date
            </p>
          </CardContent>
        </Card>
      </div>

      {/* All Subscriptions */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>All Subscriptions</CardTitle>
            <CardDescription>Manage your recurring payments and subscriptions</CardDescription>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Subscription
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {subscriptions.map((subscription) => (
              <div key={subscription.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <CreditCard className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium">{subscription.name}</h4>
                      {getStatusBadge(subscription.status)}
                    </div>
                    <p className="text-sm text-muted-foreground">{subscription.description}</p>
                    <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-1">
                      <span>{subscription.category}</span>
                      <span>•</span>
                      <span>Next: {new Date(subscription.nextPayment).toLocaleDateString()}</span>
                      <span>•</span>
                      <span>Auto-renew: {subscription.autoRenew ? 'On' : 'Off'}</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="font-semibold">
                      ${subscription.amount}/{subscription.frequency === 'monthly' ? 'mo' : subscription.frequency === 'yearly' ? 'yr' : 'wk'}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      ${(subscription.amount * getFrequencyMultiplier(subscription.frequency) / 12).toFixed(2)}/mo avg
                    </div>
                  </div>
                  
                  <div className="flex flex-col space-y-2">
                    <Button
                      size="sm"
                      variant={subscription.status === 'active' ? 'outline' : 'default'}
                      onClick={() => toggleSubscriptionStatus(subscription.id)}
                    >
                      {subscription.status === 'active' ? (
                        <>
                          <Pause className="h-3 w-3 mr-1" />
                          Pause
                        </>
                      ) : (
                        <>
                          <Play className="h-3 w-3 mr-1" />
                          Resume
                        </>
                      )}
                    </Button>
                    
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={subscription.autoRenew}
                        onCheckedChange={() => toggleAutoRenew(subscription.id)}
                      />
                      <span className="text-xs text-muted-foreground">Auto</span>
                    </div>
                  </div>
                  
                  <Button variant="ghost" size="sm">
                    <Settings className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Category Breakdown */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Category Breakdown</CardTitle>
            <CardDescription>Spending by subscription category</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(categorySummary).map(([category, amount]) => {
                const percentage = (amount / totalMonthlyAmount) * 100;
                return (
                  <div key={category} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="font-medium">{category}</span>
                      <span>${amount.toFixed(2)}/mo</span>
                    </div>
                    <Progress value={percentage} className="h-2" />
                    <div className="text-xs text-muted-foreground text-right">
                      {percentage.toFixed(1)}% of total
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Upcoming Payments</CardTitle>
            <CardDescription>Next 5 subscription payments</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {upcomingPayments.map((subscription) => (
                <div key={subscription.id} className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-sm">{subscription.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(subscription.nextPayment).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">${subscription.amount}</div>
                    <div className="text-xs text-muted-foreground">{subscription.frequency}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}