"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Bell, Search, Moon, Sun, User, LogOut, Menu } from "lucide-react";
import { useTheme } from "next-themes";

interface HeaderProps {
  activeSection: string;
  onMobileMenuToggle?: () => void;
}

export function Header({ activeSection, onMobileMenuToggle }: HeaderProps) {
  const { theme, setTheme } = useTheme();

  console.log("Header rendered for section:", activeSection);

  const getSectionTitle = (section: string) => {
    const titles: Record<string, string> = {
      dashboard: "Dashboard",
      transactions: "Transactions",
      analytics: "Analytics",
      investments: "Investment Portfolio",
      budgets: "Budget & Goals",
      subscriptions: "Subscriptions",
      receipts: "Receipt Management",
      upload: "File Upload",
      "ai-chat": "AI Financial Assistant",
      settings: "Settings",
    };
    return titles[section] || "Finance Management";
  };

  return (
    <header className="bg-card/80 backdrop-blur-xl border-b border-border/50 px-4 sm:px-6 py-4 shadow-soft">
      <div className="flex items-center justify-between">
        {/* Left side - Mobile menu + Title */}
        <div className="flex items-center space-x-4">
          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onMobileMenuToggle}
            className="lg:hidden hover:bg-muted/80 rounded-lg"
          >
            <Menu className="h-5 w-5" />
          </Button>

          <div>
            <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
              {getSectionTitle(activeSection)}
            </h1>
            <p className="text-xs sm:text-sm text-muted-foreground hidden sm:block">
              Welcome back! Here's your financial overview.
            </p>
          </div>
        </div>

        {/* Search Bar - Hidden on mobile */}
        <div className="relative max-w-sm hidden md:block">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search transactions, categories..."
            className="pl-10 w-64 lg:w-80 bg-muted/30 border-border/50 focus:bg-background transition-colors duration-300 rounded-xl"
          />
        </div>

        {/* Right side - Actions */}
        <div className="flex items-center space-x-2 sm:space-x-4">
          {/* Search button for mobile */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden hover:bg-muted/80 rounded-xl transition-all duration-300 hover:scale-105"
          >
            <Search className="h-4 w-4" />
          </Button>

          {/* Theme Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            className="hover:bg-muted/80 rounded-xl transition-all duration-300 hover:scale-105"
          >
            <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0 text-amber-500" />
            <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100 text-indigo-600" />
          </Button>

          {/* Notifications */}
          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              className="hover:bg-muted/80 rounded-xl transition-all duration-300 hover:scale-105"
            >
              <Bell className="h-4 w-4" />
            </Button>
            <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-gradient-to-r from-destructive to-warning text-white text-xs animate-pulse-soft">
              3
            </Badge>
          </div>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="relative h-10 w-10 rounded-full hover:shadow-medium transition-all duration-300 hover:scale-105"
              >
                <Avatar className="h-10 w-10 ring-2 ring-primary/20">
                  <AvatarImage src="/avatar.png" alt="User" />
                  <AvatarFallback className="bg-gradient-primary text-white font-semibold">
                    JD
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-56 bg-card/90 backdrop-blur-xl border-border/50 shadow-strong"
              align="end"
              forceMount
            >
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">John Doe</p>
                  <p className="text-xs leading-none text-muted-foreground">
                    <EMAIL>
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-border/50" />
              <DropdownMenuItem className="hover:bg-muted/80 transition-colors duration-200">
                <User className="mr-2 h-4 w-4 text-primary" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-destructive/10 text-destructive transition-colors duration-200">
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
