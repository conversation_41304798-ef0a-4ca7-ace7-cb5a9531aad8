"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  TrendingUp, 
  TrendingDown, 
  Plus, 
  Target, 
  DollarSign, 
  Calendar,
  Edit,
  CheckCircle2
} from 'lucide-react';

interface Investment {
  id: string;
  symbol: string;
  name: string;
  shares: number;
  currentPrice: number;
  totalValue: number;
  dayChange: number;
  dayChangePercent: number;
  totalReturn: number;
  totalReturnPercent: number;
}

interface Goal {
  id: string;
  name: string;
  targetAmount: number;
  currentAmount: number;
  deadline: string;
  category: string;
}

export function InvestmentPortfolio() {
  const [showAddGoal, setShowAddGoal] = useState(false);

  console.log("InvestmentPortfolio component rendered");

  const investments: Investment[] = [
    {
      id: '1',
      symbol: 'AAPL',
      name: 'Apple Inc.',
      shares: 25,
      currentPrice: 185.20,
      totalValue: 4630.00,
      dayChange: 2.15,
      dayChangePercent: 1.17,
      totalReturn: 630.00,
      totalReturnPercent: 15.75,
    },
    {
      id: '2',
      symbol: 'MSFT',
      name: 'Microsoft Corporation',
      shares: 15,
      currentPrice: 378.85,
      totalValue: 5682.75,
      dayChange: -5.20,
      dayChangePercent: -1.35,
      totalReturn: 1182.75,
      totalReturnPercent: 26.27,
    },
    {
      id: '3',
      symbol: 'GOOGL',
      name: 'Alphabet Inc.',
      shares: 10,
      currentPrice: 142.56,
      totalValue: 1425.60,
      dayChange: 3.25,
      dayChangePercent: 2.33,
      totalReturn: 225.60,
      totalReturnPercent: 18.80,
    },
    {
      id: '4',
      symbol: 'TSLA',
      name: 'Tesla Inc.',
      shares: 8,
      currentPrice: 210.45,
      totalValue: 1683.60,
      dayChange: -8.15,
      dayChangePercent: -3.73,
      totalReturn: -316.40,
      totalReturnPercent: -15.83,
    },
  ];

  const goals: Goal[] = [
    {
      id: '1',
      name: 'Emergency Fund',
      targetAmount: 10000,
      currentAmount: 8760,
      deadline: '2024-12-31',
      category: 'Safety',
    },
    {
      id: '2',
      name: 'Vacation Fund',
      targetAmount: 5000,
      currentAmount: 2300,
      deadline: '2024-08-15',
      category: 'Travel',
    },
    {
      id: '3',
      name: 'New Car Down Payment',
      targetAmount: 15000,
      currentAmount: 6200,
      deadline: '2025-06-01',
      category: 'Transportation',
    },
    {
      id: '4',
      name: 'Home Improvement',
      targetAmount: 8000,
      currentAmount: 8000,
      deadline: '2024-05-01',
      category: 'Home',
    },
  ];

  const totalPortfolioValue = investments.reduce((sum, inv) => sum + inv.totalValue, 0);
  const totalDayChange = investments.reduce((sum, inv) => sum + (inv.dayChange * inv.shares), 0);
  const totalDayChangePercent = (totalDayChange / (totalPortfolioValue - totalDayChange)) * 100;

  return (
    <div className="space-y-6">
      {/* Portfolio Overview */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Portfolio Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalPortfolioValue.toLocaleString()}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <span>Total Investment</span>
              <Badge variant={totalDayChangePercent >= 0 ? 'default' : 'destructive'}>
                {totalDayChangePercent >= 0 ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
                {totalDayChangePercent.toFixed(2)}%
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Day Change</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${totalDayChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {totalDayChange >= 0 ? '+' : ''}${totalDayChange.toFixed(2)}
            </div>
            <p className="text-xs text-muted-foreground">
              Since yesterday's close
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Goals</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{goals.filter(g => g.currentAmount < g.targetAmount).length}</div>
            <p className="text-xs text-muted-foreground">
              Goals in progress
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Holdings */}
      <Card>
        <CardHeader>
          <CardTitle>Holdings</CardTitle>
          <CardDescription>Your current investment positions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {investments.map((investment) => (
              <div key={investment.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-sm font-semibold text-primary">{investment.symbol}</span>
                  </div>
                  <div>
                    <h4 className="font-medium">{investment.name}</h4>
                    <p className="text-sm text-muted-foreground">
                      {investment.shares} shares × ${investment.currentPrice}
                    </p>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="font-semibold">${investment.totalValue.toLocaleString()}</div>
                  <div className={`text-sm flex items-center ${
                    investment.dayChangePercent >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {investment.dayChangePercent >= 0 ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
                    {investment.dayChangePercent >= 0 ? '+' : ''}{investment.dayChangePercent.toFixed(2)}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Financial Goals */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Financial Goals</CardTitle>
            <CardDescription>Track your savings progress</CardDescription>
          </div>
          <Dialog open={showAddGoal} onOpenChange={setShowAddGoal}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Goal
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Goal</DialogTitle>
                <DialogDescription>Set up a new financial goal to track your progress</DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="goalName">Goal Name</Label>
                  <Input id="goalName" placeholder="e.g., Vacation Fund" />
                </div>
                <div>
                  <Label htmlFor="targetAmount">Target Amount</Label>
                  <Input id="targetAmount" type="number" placeholder="5000" />
                </div>
                <div>
                  <Label htmlFor="currentAmount">Current Amount</Label>
                  <Input id="currentAmount" type="number" placeholder="1200" />
                </div>
                <div>
                  <Label htmlFor="deadline">Target Date</Label>
                  <Input id="deadline" type="date" />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setShowAddGoal(false)}>Cancel</Button>
                  <Button onClick={() => setShowAddGoal(false)}>Create Goal</Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            {goals.map((goal) => {
              const progress = (goal.currentAmount / goal.targetAmount) * 100;
              const isCompleted = goal.currentAmount >= goal.targetAmount;
              const remaining = goal.targetAmount - goal.currentAmount;
              
              return (
                <div key={goal.id} className="space-y-3 p-4 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium">{goal.name}</h4>
                      {isCompleted && <CheckCircle2 className="h-4 w-4 text-green-500" />}
                    </div>
                    <Badge variant="outline">{goal.category}</Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>${goal.currentAmount.toLocaleString()}</span>
                      <span>${goal.targetAmount.toLocaleString()}</span>
                    </div>
                    <Progress value={Math.min(progress, 100)} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>{progress.toFixed(1)}% complete</span>
                      <span>Due: {new Date(goal.deadline).toLocaleDateString()}</span>
                    </div>
                  </div>
                  
                  {!isCompleted && (
                    <div className="text-sm text-muted-foreground">
                      ${remaining.toLocaleString()} remaining
                    </div>
                  )}
                  
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline" className="flex-1">
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1">
                      <Plus className="h-3 w-3 mr-1" />
                      Add Funds
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}